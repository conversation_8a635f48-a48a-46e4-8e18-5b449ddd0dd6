apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: anissa-services-dev
  namespace: argocd
spec:
  project: anissa-services
  destination:
    server: https://kubernetes.default.svc
    namespace: anissa-services-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/services/auth/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/services/user/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/main/dev/ingress
      targetRevision: main



