apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anissa-services-ingress-dev
  namespace: anissa-services-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: anissa-services-dev
spec:
  ingressClassName: nginx
  rules:
    - host: auth-dev.anissa.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: anissa-auth-dev
                port:
                  number: 80
    - host: user-dev.anissa.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: anissa-user-dev
                port:
                  number: 80