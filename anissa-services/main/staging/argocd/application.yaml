apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: anissa-services-staging
  namespace: argocd
spec:
  project: anissa-services
  destination:
    server: https://kubernetes.default.svc
    namespace: anissa-services-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/services/auth/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/services/user/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: anissa-services/main/staging/ingress
      targetRevision: main



