apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anissa-services-ingress-staging
  namespace: anissa-services-staging
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: anissa-services-staging
spec:
  ingressClassName: nginx
  rules:
    - host: auth-staging.anissa.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: anissa-auth-staging
                port:
                  number: 80
    - host: user-staging.anissa.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: anissa-user-staging
                port:
                  number: 80