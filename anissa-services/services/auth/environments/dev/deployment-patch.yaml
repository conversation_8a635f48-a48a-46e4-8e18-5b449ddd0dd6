---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth
  template:
    spec:
      containers:
        - name: anissa-auth
          env:
            - name: spring.profiles.active
              value: "dev"
            - name: spring.datasource.url
              value: "******************************************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: "AVNS_pp7dgRMgD4Y8NO6dYDe"
            - name: spring.liquibase.enabled
              value: "false"
            - name: app.oauth2.redirectUri
              value: "https://app-dev.shoonbot.ai/en/authorized,https://app-dev.shoonbot.ai/th/authorized,http://localhost:3900/en/authorized,http://localhost:3900/th/authorized"
            - name: app.oauth2.appUrl
              value: "https://app-dev.shoonbot.ai"