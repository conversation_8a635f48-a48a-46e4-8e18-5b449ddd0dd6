---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: user
    spec:
      containers:
        - name: anissa-user
          image: omadar/anissa-user-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8082
