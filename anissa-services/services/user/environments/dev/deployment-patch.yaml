---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user
  template:
    spec:
      containers:
        - name: anissa-user
          env:
            - name: spring.profiles.active
              value: "dev"
            - name: spring.datasource.url
              value: "******************************************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: "AVNS_pp7dgRMgD4Y8NO6dYDe"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: 'https://auth-dev.anissa.ai'
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: 'https://auth-dev.anissa.ai/.well-known/jwks.json'