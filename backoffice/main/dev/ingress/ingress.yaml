apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backoffice-ingress-dev
  namespace: backoffice-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: backoffice-dev
spec:
  ingressClassName: nginx
  rules:
    - host: bo-dev.lucablock.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backoffice-admin-dev
                port:
                  number: 80
    - host: bo-api-dev.lucablock.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backoffice-api-dev
                port:
                  number: 80