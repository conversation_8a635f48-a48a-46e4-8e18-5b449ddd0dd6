apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backoffice-ingress-staging
  namespace: backoffice-staging
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: backoffice-staging
spec:
  ingressClassName: nginx
  rules:
    - host: bo-staging.lucablock.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backoffice-admin-staging
                port:
                  number: 80
    - host: bo-api-staging.lucablock.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backoffice-api-staging
                port:
                  number: 80