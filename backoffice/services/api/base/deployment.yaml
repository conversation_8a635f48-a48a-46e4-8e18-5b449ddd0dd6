---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
        - name: backoffice-api
          image: omadar/backoffice-api-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
