---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      containers:
        - name: backoffice-api
          env:
            - name: spring.profiles.active
              value: "dev"
              #            - name: spring.datasource.url
              #              value: "**********************************************************************************************************************************************************************************************************************************"
              #            - name: spring.datasource.username
              #              value: "doadmin"
              #            - name: spring.datasource.password
              #              value: "AVNS_l9inmCDZWyDQjaqf4wy"
              #            - name: spring.liquibase.enabled
              #              value: "false"
            - name: spring.jpa.data.redis.host
              value: "backoffice-redis-dev-0.backoffice-redis-dev.backoffice-dev.svc.cluster.local"
            - name: spring.jpa.data.redis.port
              value: "6379"
            - name: spring.jpa.data.redis.database
              value: "0"
            - name: s3.url
              value: "https://cdn.lucablock.io"