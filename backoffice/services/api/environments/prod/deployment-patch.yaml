---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      volumes:
        - name: service-account-credentials
          secret:
            secretName: service-account-credentials
      containers:
        - name: backoffice-api
          volumeMounts:
            - name: service-account-credentials
              mountPath: /secrets/
              readOnly: true
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.datasource.url
              value: "jdbc:postgresql://**********:5432/prod?searchpath=public&createDatabaseIfNotExist=true&useUnicode=yes&characterEncoding=UTF-8&serverTimezone=Asia/Bangkok&prepareThreshold=0"
            - name: spring.datasource.username
              value: "develop"
            - name: spring.datasource.password
              value: "U28Yt2EUpnO:okHx"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.jpa.data.redis.host
              value: "backoffice-redis-0.backoffice-redis.backoffice-prod.svc.cluster.local"
            - name: spring.jpa.data.redis.port
              value: "6379"
            - name: spring.jpa.data.redis.database
              value: "0"
            - name: s3.url
              value: "https://cdn.lucablock.io"
            - name: google.translate.credentials
              value: "/secrets/lucablock-ops-a7d5135a3327.json"