module "honconnect" {
  source = "./modules/cloudflare"
  ip_address = "*************"
  cloudflare_api_token = var.cloudflare_api_token
  cloudflare_zone_id = var.cloudflare_honconnect_zone_id
  sub_domain_list = ["app-staging","admin-staging","auth-staging","api-staging","stock-staging","order-staging","estimate-staging"]
}

module "lucablock" {
  source = "./modules/cloudflare"
  ip_address = "*************"
  cloudflare_api_token = var.cloudflare_api_token
  cloudflare_zone_id = var.cloudflare_lucablock_zone_id
  sub_domain_list = ["bo-staging","bo-api-staging"]
}

# module "digiboxs" {
#   source = "./modules/cloudflare"
#   ip_address = "*************"
#   cloudflare_api_token = var.cloudflare_api_token
#   cloudflare_zone_id = var.cloudflare_digiboxs_zone_id
#   sub_domain_list = ["app-test","admin-test","api-test"]
# }