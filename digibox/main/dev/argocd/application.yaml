apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: digibox-dev
  namespace: argocd
spec:
  project: digiboxs
  destination:
    server: https://kubernetes.default.svc
    namespace: digibox-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/api/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/web/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/landing/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/admin/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/main/dev/ingress
      targetRevision: main



