apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: digibox-ingress-dev
  namespace: digibox-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: digibox-dev
spec:
  ingressClassName: nginx
  rules:
    - host: app-dev.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-web-dev
                port:
                  number: 80
    - host: dev.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-landing-dev
                port:
                  number: 80
    - host: api-dev.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-api-dev
                port:
                  number: 80
    - host: admin-dev.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-admin-dev
                port:
                  number: 80