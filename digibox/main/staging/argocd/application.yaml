apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: digibox-staging
  namespace: argocd
spec:
  project: digiboxs
  destination:
    server: https://kubernetes.default.svc
    namespace: digibox-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/api/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/web/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/landing/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/services/admin/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: digibox/main/staging/ingress
      targetRevision: main



