apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: digibox-ingress-staging
  namespace: digibox-staging
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: digibox-staging
spec:
  ingressClassName: nginx
  rules:
    - host: app-staging.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-web-staging
                port:
                  number: 80
    - host: staging.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-landing-staging
                port:
                  number: 80
    - host: api-staging.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-api-staging
                port:
                  number: 80
    - host: admin-staging.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: digibox-admin-staging
                port:
                  number: 80