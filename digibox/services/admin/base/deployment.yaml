---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: admin
    spec:
      containers:
        - name: digibox-admin
          image: omadar/digibox-admin-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
