---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      containers:
        - name: digibox-api
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.jpa.data.redis.host
              value: "digibox-redis-0.digibox-redis.digibox-prod.svc.cluster.local"
            - name: spring.jpa.data.redis.port
              value: "6379"
            - name: spring.jpa.data.redis.database
              value: "0"
            - name: spring.datasource.url
              value: "jdbc:postgresql://**********:5432/digibox-prod?searchpath=public&createDatabaseIfNotExist=true&useUnicode=yes&characterEncoding=UTF-8&serverTimezone=Asia/Bangkok&prepareThreshold=0"
            - name: spring.datasource.username
              value: "develop"
            - name: spring.datasource.password
              value: "U28Yt2EUpnO:okHx"