---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      containers:
        - name: digibox-api
          env:
            - name: spring.profiles.active
              value: "staging"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.jpa.data.redis.host
              value: "digibox-redis-staging-0.digibox-redis-staging.digibox-staging.svc.cluster.local"
            - name: spring.jpa.data.redis.port
              value: "6379"
            - name: spring.jpa.data.redis.database
              value: "0"