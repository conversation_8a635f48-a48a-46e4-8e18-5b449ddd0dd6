---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: landing
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: landing
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: landing
    spec:
      containers:
        - name: digibox-landing
          image: omadar/digibox-landing-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
