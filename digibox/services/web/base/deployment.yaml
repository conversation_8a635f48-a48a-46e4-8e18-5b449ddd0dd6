---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: web
    spec:
      containers:
        - name: digibox-web
          image: omadar/digibox-web-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
