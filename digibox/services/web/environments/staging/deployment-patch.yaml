---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web
  template:
    spec:
      containers:
        - name: digibox-web
          env:
            - name: NEXT_PUBLIC_APP_NAME
              value: "Digiboxs"
            - name: NEXT_PUBLIC_API_ENDPOINT
              value: "https://api-staging.digiboxs.com/api"
            - name: NEXT_PUBLIC_OAUTH_ENDPOINT
              value: "https://api-staging.digiboxs.com"
            - name: NEXT_PUBLIC_OAUTH_REDIRECT_URL
              value: "https://app-staging.digiboxs.com/oauth2/redirect"
            - name: NEXT_PUBLIC_LINK_URL
              value: "https://app-staging.digiboxs.com/"