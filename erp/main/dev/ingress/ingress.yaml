apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: erp-ingress-dev
  namespace: erp-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: erp-dev
spec:
  ingressClassName: nginx
  rules:
    - host: master-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: erp-master-core-dev
                port:
                  number: 80
    - host: sapi-dev.digiboxs.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: erp-shop-api-dev
                port:
                  number: 80
