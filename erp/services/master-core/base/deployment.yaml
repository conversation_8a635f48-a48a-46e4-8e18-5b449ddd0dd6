---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: master-core
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: master-core
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: master-core
    spec:
      containers:
        - name: erp-master-core
          image: omadar/erp-master-core-dev:sha-4be9641
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
