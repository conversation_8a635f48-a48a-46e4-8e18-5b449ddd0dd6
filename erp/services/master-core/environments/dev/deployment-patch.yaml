---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: master-core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: master-core
  template:
    spec:
      containers:
        - name: erp-master-core
          env:
            - name: APP_ENV
              value: "develop"
            - name: APP_HOST
              value: erp-master-core-dev
            - name: APP_PORT
              value: "8080"
#            - name: PG_HOST
#              value: "db-erp-dev-do-user-6486567-0.m.db.ondigitalocean.com"
#            - name: PG_PORT
#              value: "25061"
#            - name: PG_USERNAME
#              value: "doadmin"
#            - name: PG_PASSWORD
#              value: "AVNS_-tdsD1vEMxWDwjZaw47"
#            - name: PG_DATABASE
#              value: "master-core-dev"
#            - name: PG_SSL_MODE
#              value: "require"
#            - name: PG_SCHEMA
#              value: "public"
            - name: PG_CONN_STR
              value: "postgresql://doadmin:<EMAIL>:25061/master-core-dev?sslmode=require"
            - name: API_KEY
              value: "bHVjYWJsb2NrLW1hc3Rlci1jb3Jl"
            - name: S3_ACCESS_KEY_ID
              value: "********************"
            - name: S3_ACCESS_KEY_SECRET
              value: "v1Vop0IkVN+u/uzJMMYceM72L8eFjMzBHHswJ+9y"
            - name: S3_REGION_NAME
              value: "ap-southeast-1"
            - name: S3_BUCKET_NAME
              value: "kabaodevbuckets"
            - name: S3_FOLDER_NAME
              value: "/test"
            - name: S3_FOLDER_BUFFER
              value: "/test"
            - name: S3_CDN_URL
              value: "https://media.kabao.dev"