---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: shop-api
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shop-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: shop-api
    spec:
      containers:
        - name: erp-shop-api
          image: omadar/erp-shop-api-dev:sha-4be9641
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
