---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: shop-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shop-api
  template:
    spec:
      containers:
        - name: erp-shop-api
          env:
            - name: APP_HOST
              value: "https://sapi-dev.digiboxs.com"
            - name: CLIENT_HOST
              value: "http://localhost:3000"
            - name: PG_DB_URL
              value: "postgresql://doadmin:<EMAIL>:25061/shop-api-dev?sslmode=require"
            - name: REDIS_HOST
              value: "erp-redis-dev-0.erp-redis-dev.erp-dev.svc.cluster.local"
            - name:  REDIS_PORT
              value: "6379"
            - name: REDIS_DB
              value: "0"
            - name: S3_ACCESS_KEY_ID
              value: "********************"
            - name: S3_ACCESS_KEY_SECRET
              value: "PpXqQIsmizlHPKJdetlqypnZmlaI79J7Tt1rfhIx"
            - name: S3_REGION_NAME
              value: "ap-southeast-1"
            - name: S3_BUCKET_NAME
              value: "digiboxs"
            - name: S3_FOLDER_NAME
              value: "/dev/erp-shop"
            - name: S3_FOLDER_BUFFER
              value: "/dev/buffer"
            - name: S3_CDN_URL
              value: "https://cdn.digiboxs.com"
            - name: JWT_SECRET
              value: "DIGIBOXS"
            - name: ENCRYPTION_SECRET
              value: "passphrasewhichneedstobe32bytes!"
            - name: SHOP_ID
              value: "1"