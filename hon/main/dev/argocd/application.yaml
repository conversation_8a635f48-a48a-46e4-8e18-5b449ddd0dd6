apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: hon-dev
  namespace: argocd
spec:
  project: hon
  destination:
    server: https://kubernetes.default.svc
    namespace: hon-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/admin/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/app/environments/dev
      targetRevision: main
#    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
#      path: hon/services/auth/environments/dev
#      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/api/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/stock/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/order/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/est-lays/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/landing/environments/dev
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/main/dev/ingress
      targetRevision: main




