apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hon-ingress-dev
  namespace: hon-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: hon-dev
spec:
  ingressClassName: nginx
  rules:
    - host: app-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-app-dev
                port:
                  number: 80
    - host: admin-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-admin-dev
                port:
                  number: 80
#    - host: auth-dev.honconnect.co
#      http:
#        paths:
#          - path: /
#            pathType: Prefix
#            backend:
#              service:
#                name: hon-auth-dev
#                port:
#                  number: 80
    - host: api-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-api-dev
                port:
                  number: 80
    - host: stock-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-stock-dev
                port:
                  number: 80
    - host: order-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-order-dev
                port:
                  number: 80
    - host: estimate-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-est-lays-dev
                port:
                  number: 80
    - host: landing-dev.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-landing-dev
                port:
                  number: 80