apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: hon-prod
  namespace: argocd
spec:
  project: hon
  destination:
    server: https://kubernetes.default.svc
    namespace: hon-prod
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/admin/environments/prod-do
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/app/environments/prod-do
      targetRevision: main
#    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
#      path: hon/services/auth/environments/prod-do
#      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/api/environments/prod-do
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/stock/environments/prod-do
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/order/environments/prod-do
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/est-lays/environments/prod-do
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/main/prod-do/ingress
      targetRevision: main



