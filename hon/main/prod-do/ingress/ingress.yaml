apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hon-ingress
  namespace: hon-prod
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: hon-prod
spec:
  ingressClassName: nginx
  rules:
    - host: app.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-app
                port:
                  number: 80
    - host: admin.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-admin
                port:
                  number: 80
#    - host: auth.honconnect.co
#      http:
#        paths:
#          - path: /
#            pathType: Prefix
#            backend:
#              service:
#                name: hon-auth
#                port:
#                  number: 80
    - host: api.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-api
                port:
                  number: 80
    - host: stock.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-stock
                port:
                  number: 80
    - host: order.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-order
                port:
                  number: 80
    - host: estimate.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-est-lays
                port:
                  number: 80