apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: hon-staging
  namespace: argocd
spec:
  project: hon
  destination:
    server: https://kubernetes.default.svc
    namespace: hon-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/admin/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/app/environments/staging
      targetRevision: main
#    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
#      path: hon/services/auth/environments/staging
#      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/api/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/stock/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/order/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/est-lays/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/services/landing/environments/staging
      targetRevision: main
    - repoURL: https://github.com/Omadar/lucablock-ops-kustomize.git
      path: hon/main/staging/ingress
      targetRevision: main



