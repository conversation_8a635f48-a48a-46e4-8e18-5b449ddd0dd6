apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hon-ingress-staging
  namespace: hon-staging
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: hon-staging
spec:
  ingressClassName: nginx
  rules:
    - host: app-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-app-staging
                port:
                  number: 80
    - host: admin-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-admin-staging
                port:
                  number: 80
#    - host: auth-staging.honconnect.co
#      http:
#        paths:
#          - path: /
#            pathType: Prefix
#            backend:
#              service:
#                name: hon-auth-staging
#                port:
#                  number: 80
    - host: api-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-api-staging
                port:
                  number: 80
    - host: stock-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-stock-staging
                port:
                  number: 80
    - host: order-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-order-staging
                port:
                  number: 80
    - host: estimate-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-est-lays-staging
                port:
                  number: 80
    - host: landing-staging.honconnect.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hon-landing-staging
                port:
                  number: 80