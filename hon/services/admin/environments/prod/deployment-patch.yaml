---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin
  template:
    spec:
      containers:
        - name: hon-admin
          env:
            - name: NEXT_PUBLIC_API_ENDPOINT
              value: "https://api.honconnect.co/api"
            - name: NEXT_PUBLIC_AUTH_ENDPOINT
              value: "https://auth.honconnect.co"
            - name: NEXT_PUBLIC_API_ORDER_ENDPOINT
              value: "https://order.honconnect.co/api"
            - name: NEXT_PUBLIC_API_STOCK_ENDPOINT
              value: "https://stock.honconnect.co/api"
            - name: NEXT_PUBLIC_REDIRECT_URL
              value: "http://admin.honconnect.co/authorized"
            - name: NEXT_PUBLIC_AUTH_CLIENT
              value: "hon-admin"
            - name: NEXT_PUBLIC_AUTH_SECRET
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"
