---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      containers:
        - name: hon-api
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.datasource.url
              value: "*********************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: 'AVNS_J71kQCOongmszCiUply'
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: "https://auth.honconnect.co/.well-known/jwks.json"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: "https://auth.honconnect.co"
            - name: spring.data.redis.host
              value: "hon-redis-0.hon-redis.hon-prod.svc.cluster.local"
            - name: spring.data.redis.port
              value: "6379"
            - name: spring.data.redis.database
              value: "0"
            - name: mail-sender.username
              value: "<EMAIL>"
            - name: mail-sender.password
              value: "rwko zyjy axhl dsjg"