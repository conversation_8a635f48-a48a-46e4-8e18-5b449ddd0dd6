---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    spec:
      containers:
        - name: hon-api
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.datasource.url
              value: "********************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "develop"
            - name: spring.datasource.password
              value: '60:5h@:9F^ZbdG\O'
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: "https://auth.honconnect.co/.well-known/jwks.json"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: "https://auth.honconnect.co"