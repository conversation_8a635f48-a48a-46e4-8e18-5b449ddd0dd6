---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: app
    spec:
      containers:
        - name: hon-app
          image: omadar/hon-app-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
