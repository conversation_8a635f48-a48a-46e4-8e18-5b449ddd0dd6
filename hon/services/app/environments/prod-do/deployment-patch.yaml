---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app
  template:
    spec:
      containers:
        - name: hon-app
          env:
            - name: NEXT_PUBLIC_API_ENDPOINT
              value: "https://api.honconnect.co/api"
            - name: NEXT_PUBLIC_AUTH_ENDPOINT
              value: "https://auth.honconnect.co"
            - name: NEXT_PUBLIC_API_ORDER_ENDPOINT
              value: "https://order.honconnect.co/api"
            - name: NEXT_PUBLIC_API_STOCK_ENDPOINT
              value: "https://stock.honconnect.co/api"
            - name: NEXT_PUBLIC_REDIRECT_URL
              value: "https://app.honconnect.co/authorized"
            - name: NEXT_PUBLIC_TINY_API_KEY
              value: "xjvae5nky6fugej0derbc1qc3cf42km5oupwnwbdge75vfqz"
            - name: NEXT_PUBLIC_AUTH_CLIENT
              value: "hon-app"
            - name: NEXT_PUBLIC_AUTH_SECRET
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"