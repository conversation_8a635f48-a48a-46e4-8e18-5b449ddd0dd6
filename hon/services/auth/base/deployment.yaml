---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: auth
    spec:
      containers:
        - name: hon-auth
          image: omadar/hon-auth-service-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080