---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth
  template:
    spec:
      containers:
        - name: hon-auth
          env:
            - name: spring.profiles.active
              value: "dev"
            - name: spring.datasource.url
              value: "***********************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: "AVNS_d7AwQxrlrxQZcaGLBmi"
            - name: app.oauth2.client
              value: "hon-app-dev"
            - name: app.oauth2.secret
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"
            - name: app.adminAuth.client
              value: "hon-admin-dev"
            - name: app.adminAuth.secret
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"