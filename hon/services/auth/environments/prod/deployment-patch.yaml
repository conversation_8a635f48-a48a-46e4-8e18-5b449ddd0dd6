---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth
  template:
    spec:
      containers:
        - name: hon-auth
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.datasource.url
              value: "********************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "develop"
            - name: spring.datasource.password
              value: '60:5h@:9F^ZbdG\O'
            - name: app.oauth2.client
              value: "hon-app"
            - name: app.oauth2.secret
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"
            - name: app.adminAuth.client
              value: "hon-admin"
            - name: app.adminAuth.secret
              value: "i4WzF5SVJKJRCHZEqET2BPSvNh20YVzQ"