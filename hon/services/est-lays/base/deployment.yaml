---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: est-lays
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: est-lays
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: est-lays
    spec:
      containers:
        - name: hon-est-lays
          image: omadar/hon-estimate-lays-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3500
