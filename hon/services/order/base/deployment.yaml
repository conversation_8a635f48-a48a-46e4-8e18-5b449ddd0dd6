---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: order
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: order
    spec:
      containers:
        - name: hon-order
          image: omadar/hon-order-service-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
