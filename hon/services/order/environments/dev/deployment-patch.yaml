---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order
spec:
  replicas: 1
  selector:
    matchLabels:
      app: order
  template:
    spec:
      containers:
        - name: hon-order
          env:
            - name: spring.profiles.active
              value: "dev"
            - name: spring.datasource.url
              value: "***********************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: "AVNS_d7AwQxrlrxQZcaGLBmi"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: "https://auth-dev.honconnect.co/.well-known/jwks.json"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: "https://auth-dev.honconnect.co"
            - name: spring.microservice.gateway.core-uri
              value: "https://api-dev.honconnect.co/api"
            - name: spring.microservice.gateway.order-uri
              value: "https://order-dev.honconnect.co/api"
            - name: spring.microservice.gateway.stock-uri
              value: "https://stock-dev.honconnect.co/api"
