---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order
spec:
  replicas: 1
  selector:
    matchLabels:
      app: order
  template:
    spec:
      containers:
        - name: hon-order
          env:
            - name: spring.profiles.active
              value: "staging"
            - name: spring.datasource.url
              value: "************************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: "AVNS_J71kQCOongmszCiUply"
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: "https://auth-staging.honconnect.co/.well-known/jwks.json"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: "https://auth-staging.honconnect.co"
            - name: spring.microservice.gateway.core-uri
              value: "https://api-staging.honconnect.co/api"
            - name: spring.microservice.gateway.order-uri
              value: "https://order-staging.honconnect.co/api"
            - name: spring.microservice.gateway.stock-uri
              value: "https://stock-staging.honconnect.co/api"
