apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: hon-redis-dev
  replicas: 2
  selector:
    matchLabels:
      app: redis
  template:
    spec:
      initContainers:
        - name: config
          args:
            - |
              cp /tmp/redis/redis.conf /etc/redis/redis.conf
              echo "finding master..."
              MASTER_FDQN=`hostname  -f | sed -e 's/hon-redis-dev-[0-9]\./hon-redis-dev-0./'`
              if [ "$(redis-cli -h sentinel -p 5000 ping)" != "PONG" ]; then
                echo "master not found, defaulting to hon-redis-dev-0"

                if [ "$(hostname)" == "hon-redis-dev-0" ]; then
                  echo "this is hon-redis-dev-0, not updating config..."
                else
                  echo "updating redis.conf..."
                  echo "slaveof $MASTER_FDQN 6379" >> /etc/redis/redis.conf
                fi
              else
                echo "sentinel found, finding master"
                MASTER="$(redis-cli -h sentinel -p 5000 sentinel get-master-addr-by-name mymaster | grep -E '(^hon-redis-dev-\d{1,})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})')"
                echo "master found : $MASTER, updating redis.conf"
                echo "slaveof $MASTER 6379" >> /etc/redis/redis.conf
              fi
          volumeMounts:
            - name: hon-redis-config-dev
              mountPath: /etc/redis/
            - name: config
              mountPath: /tmp/redis/
      containers:
        - name: redis
          image: redis:6.2.3-alpine
          command: [ "redis-server" ]
          args: [ "/etc/redis/redis.conf" ]
          ports:
            - containerPort: 6379
              name: redis
          volumeMounts:
            - name: data
              mountPath: /data
            - name: hon-redis-config-dev
              mountPath: /etc/redis/
      volumes:
        - name: hon-redis-config-dev
          emptyDir: { }
        - name: config
          configMap:
            name: hon-redis-config-dev
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: "hon-redis-storage-dev"
        resources:
          requests:
            storage: 500Mi
