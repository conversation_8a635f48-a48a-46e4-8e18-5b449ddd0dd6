---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: stock
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: stock
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: stock
    spec:
      containers:
        - name: hon-stock
          image: omadar/hon-stock-service-dev:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
