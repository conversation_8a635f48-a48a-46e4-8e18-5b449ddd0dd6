---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: stock
spec:
  replicas: 1
  selector:
    matchLabels:
      app: stock
  template:
    spec:
      containers:
        - name: hon-stock
          env:
            - name: spring.profiles.active
              value: "prod"
            - name: spring.datasource.url
              value: "*********************************************************************************************************************************************************************************************************************************"
            - name: spring.datasource.username
              value: "doadmin"
            - name: spring.datasource.password
              value: 'AVNS_J71kQCOongmszCiUply'
            - name: spring.liquibase.enabled
              value: "false"
            - name: spring.security.oauth2.resourceserver.jwt.jwk-set-uri
              value: "https://auth.honconnect.co/.well-known/jwks.json"
            - name: spring.security.oauth2.resourceserver.jwt.issuer-uri
              value: "https://auth.honconnect.co"
            - name: spring.microservice.gateway.core-uri
              value: "https://api.honconnect.co/api"
            - name: spring.microservice.gateway.order-uri
              value: "https://order.honconnect.co/api"
            - name: spring.microservice.gateway.stock-uri
              value: "https://stock.honconnect.co/api"
            - name: issuer
              value: "https://auth.honconnect.co"