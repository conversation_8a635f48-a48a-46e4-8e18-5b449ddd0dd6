---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lucablock-site-dev
  namespace: lucablock-site-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lucablock-site-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: lucablock-site-dev
    spec:
      containers:
        - name: lucablock-site-dev
          image: omadar/lucablock-site-dev:dev-latest
          imagePullPolicy: Always
          env:
            - name: NEXT_PUBLIC_SITE_URL
              value: "https://dev.lucablock.io"
          ports:
            - name: http
              containerPort: 3000
