---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lucablock-site
  namespace: lucablock-site
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lucablock-site
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: lucablock-site
    spec:
      containers:
        - name: lucablock-site
          image: omadar/lucablock-site:latest
          imagePullPolicy: Always
          env:
            - name: NEXT_PUBLIC_SITE_URL
              value: "https://lucablock.io"
          ports:
            - name: http
              containerPort: 3000
