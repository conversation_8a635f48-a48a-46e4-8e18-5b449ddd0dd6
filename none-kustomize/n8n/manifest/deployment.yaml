apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: n8n-dev
  name: n8n-server
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
  selector:
    matchLabels:
      app: n8n-server
  template:
    metadata:
      labels:
        app: n8n-server
    spec:
      containers:
        - name: n8n-server
          image: docker.n8n.io/n8nio/n8n:1.98.0
          imagePullPolicy: Always
          ports:
            - containerPort: 5678
          env:
            - name: N8N_HOST
              value: "n8n-dev.anissa.ai"
            - name: N8N_PROTOCOL
              value: "https"
            - name: NODE_ENV
              value: "production"
            - name: DB_TYPE
              value: "postgresdb"
            - name: DB_POSTGRESDB_HOST
              value: "db-erp-dev-do-user-6486567-0.m.db.ondigitalocean.com"
            - name: DB_POSTGRESDB_PORT
              value: "25061"
            - name: DB_POSTGRESDB_DATABASE
              value: "n8n-dev"
            - name: DB_POSTGRESDB_USER
              value: "doadmin"
            - name: DB_POSTGRESDB_PASSWORD
              value: "AVNS_-tdsD1vEMxWDwjZaw47"
            - name: DB_POSTGRESDB_SSL_ENABLED
              value: "true"
            - name: N8N_RUNNERS_ENABLED
              value: "true"
            - name: N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS
              value: "false"
            - name: DB_POSTGRESDB_SSL_CA
              valueFrom:
                secretKeyRef:
                  name: n8n-secrets
                  key: DB_POSTGRESDB_SSL_CA
