apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: n8n-server
  namespace: n8n-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 128m
  labels:
    app.kubernetes.io/instance: n8n-server
spec:
  ingressClassName: nginx
  rules:
    - host: n8n-dev.anissa.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: n8n-server
                port:
                  number: 5678
