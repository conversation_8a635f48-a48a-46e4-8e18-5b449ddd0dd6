config:
  database:
    type: postgresdb
    postgresdb:
      host: db-erp-dev-do-user-6486567-0.m.db.ondigitalocean.com
secret:
  database:
    postgresdb:
      user: 'doadmin'
      password: 'AVNS_-tdsD1vEMxWDwjZaw47'
      database: 'n8n-dev'
      port: '25061'
      ssl: true
      sslmode: 'require'
image:
  tag: "1.88.0"
#config:
#  database:
#    type: postgresdb
#    postgresdb:
#      database: n8n-dev
#      host: db-erp-dev-do-user-6486567-0.m.db.ondigitalocean.com
#      password: AVNS_-tdsD1vEMxWDwjZaw47
#      port: 25061
#      user: doadmin
#      schema: public

#image:
#  repository: n8nio/n8n
#  pullPolicy: IfNotPresent
#  tag: "1.88.0"
#
#extraEnv:
#  NODE_ENV: "production"
#  N8N_CONCURRENCY_PRODUCTION_LIMIT: "1"
#  GENERIC_TIMEZONE: "Asia/Bangkok"


#  helm upgrade my-n8n oci://8gears.container-registry.com/library/n8n -f none-kustomize/n8n/values.yaml -n n8n-dev
#  helm uninstall my-n8n -n n8n-dev
#  helm install n8n ./none-kustomize/n8n/chart.yaml -f ./none-kustomize/n8n/values.yaml -n n8n-dev
