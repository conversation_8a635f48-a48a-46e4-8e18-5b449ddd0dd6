apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv1
  namespace: default
spec:
  storageClassName: redis-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data1"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv2
  namespace: default
spec:
  storageClassName: redis-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data2"

---

apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv3
  namespace: default
spec:
  storageClassName: redis-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/var/lib/data3"
